package com.elearning.dao;

import com.elearning.entity.Course;
import com.elearning.util.DatabaseConnection;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 课程数据访问对象
 */
public class CourseDAO {
    
    /**
     * 添加课程
     */
    public boolean addCourse(Course course) {
        String sql = "INSERT INTO courses (course_name, course_description, teacher_id, create_time, is_active) VALUES (?, ?, ?, NOW(), 1)";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, course.getCourseName());
            pstmt.setString(2, course.getCourseDescription());
            pstmt.setInt(3, course.getTeacherId());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取所有课程
     */
    public List<Course> getAllCourses() {
        List<Course> courses = new ArrayList<>();
        String sql = "SELECT c.*, u.real_name as teacher_name FROM courses c " +
                    "LEFT JOIN users u ON c.teacher_id = u.user_id " +
                    "WHERE c.is_active = 1 ORDER BY c.create_time DESC";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql);
             ResultSet rs = pstmt.executeQuery()) {
            
            while (rs.next()) {
                Course course = new Course();
                course.setCourseId(rs.getInt("course_id"));
                course.setCourseName(rs.getString("course_name"));
                course.setCourseDescription(rs.getString("course_description"));
                course.setTeacherId(rs.getInt("teacher_id"));
                course.setTeacherName(rs.getString("teacher_name"));
                course.setCreateTime(rs.getTimestamp("create_time"));
                course.setActive(rs.getBoolean("is_active"));
                course.setCourseImage(rs.getString("course_image"));
                courses.add(course);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return courses;
    }
    
    /**
     * 根据教师ID获取课程
     */
    public List<Course> getCoursesByTeacherId(int teacherId) {
        List<Course> courses = new ArrayList<>();
        String sql = "SELECT c.*, u.real_name as teacher_name FROM courses c " +
                    "LEFT JOIN users u ON c.teacher_id = u.user_id " +
                    "WHERE c.teacher_id = ? AND c.is_active = 1 ORDER BY c.create_time DESC";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, teacherId);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                Course course = new Course();
                course.setCourseId(rs.getInt("course_id"));
                course.setCourseName(rs.getString("course_name"));
                course.setCourseDescription(rs.getString("course_description"));
                course.setTeacherId(rs.getInt("teacher_id"));
                course.setTeacherName(rs.getString("teacher_name"));
                course.setCreateTime(rs.getTimestamp("create_time"));
                course.setActive(rs.getBoolean("is_active"));
                course.setCourseImage(rs.getString("course_image"));
                courses.add(course);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return courses;
    }
    
    /**
     * 根据ID获取课程
     */
    public Course getCourseById(int courseId) {
        String sql = "SELECT c.*, u.real_name as teacher_name FROM courses c " +
                    "LEFT JOIN users u ON c.teacher_id = u.user_id " +
                    "WHERE c.course_id = ? AND c.is_active = 1";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, courseId);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                Course course = new Course();
                course.setCourseId(rs.getInt("course_id"));
                course.setCourseName(rs.getString("course_name"));
                course.setCourseDescription(rs.getString("course_description"));
                course.setTeacherId(rs.getInt("teacher_id"));
                course.setTeacherName(rs.getString("teacher_name"));
                course.setCreateTime(rs.getTimestamp("create_time"));
                course.setActive(rs.getBoolean("is_active"));
                course.setCourseImage(rs.getString("course_image"));
                return course;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * 更新课程
     */
    public boolean updateCourse(Course course) {
        String sql = "UPDATE courses SET course_name = ?, course_description = ? WHERE course_id = ?";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, course.getCourseName());
            pstmt.setString(2, course.getCourseDescription());
            pstmt.setInt(3, course.getCourseId());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 删除课程（软删除）
     */
    public boolean deleteCourse(int courseId) {
        String sql = "UPDATE courses SET is_active = 0 WHERE course_id = ?";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, courseId);
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
}
