<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="com.elearning.util.DatabaseConnection" %>
<%@ page import="java.sql.Connection" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>系统测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        h3 {
            color: #34495e;
            margin-top: 0;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #3498db;
            text-decoration: none;
            padding: 10px 20px;
            border: 1px solid #3498db;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .back-link a:hover {
            background: #3498db;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>E-Learning 系统测试</h1>
        
        <!-- JSP 基本功能测试 -->
        <div class="test-item success">
            <h3>✓ JSP 基本功能</h3>
            <p>JSP页面正常运行，当前时间: <%= new java.util.Date() %></p>
        </div>
        
        <!-- 字符编码测试 -->
        <div class="test-item success">
            <h3>✓ 字符编码测试</h3>
            <p>中文显示正常: 欢迎使用E-Learning在线学习平台！</p>
        </div>
        
        <!-- 数据库连接测试 -->
        <%
            boolean dbConnected = false;
            String dbMessage = "";
            Connection testConn = null;
            
            try {
                testConn = DatabaseConnection.getConnection();
                if (testConn != null && !testConn.isClosed()) {
                    dbConnected = true;
                    dbMessage = "数据库连接成功！";
                } else {
                    dbMessage = "数据库连接失败：连接为空或已关闭";
                }
            } catch (Exception e) {
                dbMessage = "数据库连接异常：" + e.getMessage();
            } finally {
                if (testConn != null) {
                    try {
                        testConn.close();
                    } catch (Exception e) {
                        // 忽略关闭异常
                    }
                }
            }
        %>
        
        <div class="test-item <%= dbConnected ? "success" : "error" %>">
            <h3><%= dbConnected ? "✓" : "✗" %> 数据库连接测试</h3>
            <p><%= dbMessage %></p>
            <% if (!dbConnected) { %>
                <p><strong>解决方案：</strong></p>
                <ul>
                    <li>确保MySQL服务已启动</li>
                    <li>检查数据库连接参数（用户名、密码、端口）</li>
                    <li>确认数据库 'elearning' 已创建</li>
                    <li>运行数据库初始化脚本：mysql -u root -p elearning < database/elearning.sql</li>
                </ul>
            <% } %>
        </div>
        
        <!-- Session 测试 -->
        <%
            String sessionInfo = "";
            if (session.getAttribute("user") != null) {
                sessionInfo = "用户已登录：" + session.getAttribute("realName") + " (" + session.getAttribute("userType") + ")";
            } else {
                sessionInfo = "用户未登录";
            }
        %>
        
        <div class="test-item info">
            <h3>ℹ Session 状态</h3>
            <p><%= sessionInfo %></p>
            <p>Session ID: <%= session.getId() %></p>
        </div>
        
        <!-- 系统信息 -->
        <div class="test-item info">
            <h3>ℹ 系统信息</h3>
            <p><strong>Java版本:</strong> <%= System.getProperty("java.version") %></p>
            <p><strong>服务器信息:</strong> <%= application.getServerInfo() %></p>
            <p><strong>应用路径:</strong> <%= request.getContextPath() %></p>
            <p><strong>字符编码:</strong> <%= request.getCharacterEncoding() %></p>
        </div>
        
        <!-- 快速链接 -->
        <div class="test-item info">
            <h3>ℹ 快速链接</h3>
            <p>
                <a href="<%= request.getContextPath() %>/">首页</a> | 
                <a href="<%= request.getContextPath() %>/login.jsp">登录</a> | 
                <a href="<%= request.getContextPath() %>/admin/dashboard.jsp">管理员</a> | 
                <a href="<%= request.getContextPath() %>/teacher/dashboard.jsp">教师</a> | 
                <a href="<%= request.getContextPath() %>/student/dashboard.jsp">学生</a>
            </p>
        </div>
        
        <div class="back-link">
            <a href="<%= request.getContextPath() %>/">返回首页</a>
        </div>
    </div>
</body>
</html>
