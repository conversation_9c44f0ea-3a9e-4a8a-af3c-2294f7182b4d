package com.elearning.servlet;

import com.elearning.dao.UserDAO;
import com.elearning.entity.User;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 用户管理Servlet
 */
@WebServlet("/admin/userManagement")
public class UserManagementServlet extends HttpServlet {
    private UserDAO userDAO = new UserDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // 检查管理员权限
        if (!checkAdminPermission(request, response)) {
            return;
        }
        
        String action = request.getParameter("action");
        
        if ("delete".equals(action)) {
            deleteUser(request, response);
        } else {
            // 默认显示用户列表
            showUserList(request, response);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // 检查管理员权限
        if (!checkAdminPermission(request, response)) {
            return;
        }
        
        request.setCharacterEncoding("UTF-8");
        
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addUser(request, response);
        }
    }
    
    /**
     * 检查管理员权限
     */
    private boolean checkAdminPermission(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        HttpSession session = request.getSession();
        String userType = (String) session.getAttribute("userType");
        
        if (!"admin".equals(userType)) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return false;
        }
        return true;
    }
    
    /**
     * 显示用户列表
     */
    private void showUserList(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        List<User> users = userDAO.getAllUsers();
        request.setAttribute("users", users);
        request.getRequestDispatcher("/admin/userManagement.jsp").forward(request, response);
    }
    
    /**
     * 添加用户
     */
    private void addUser(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String username = request.getParameter("username");
        String password = request.getParameter("password");
        String realName = request.getParameter("realName");
        String email = request.getParameter("email");
        String userType = request.getParameter("userType");
        
        // 验证输入
        if (username == null || username.trim().isEmpty() ||
            password == null || password.trim().isEmpty() ||
            realName == null || realName.trim().isEmpty() ||
            userType == null || userType.trim().isEmpty()) {
            
            request.setAttribute("error", "所有字段都不能为空");
            showUserList(request, response);
            return;
        }
        
        // 检查用户名是否已存在
        if (userDAO.isUsernameExists(username.trim())) {
            request.setAttribute("error", "用户名已存在");
            showUserList(request, response);
            return;
        }
        
        // 创建用户对象
        User user = new User(username.trim(), password, realName.trim(), email, userType);
        
        // 添加用户
        if (userDAO.addUser(user)) {
            request.setAttribute("success", "用户添加成功");
        } else {
            request.setAttribute("error", "用户添加失败");
        }
        
        showUserList(request, response);
    }
    
    /**
     * 删除用户
     */
    private void deleteUser(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String userIdStr = request.getParameter("userId");
        
        try {
            int userId = Integer.parseInt(userIdStr);
            
            if (userDAO.deleteUser(userId)) {
                request.setAttribute("success", "用户删除成功");
            } else {
                request.setAttribute("error", "用户删除失败");
            }
        } catch (NumberFormatException e) {
            request.setAttribute("error", "无效的用户ID");
        }
        
        showUserList(request, response);
    }
}
