package com.elearning.entity;

import java.sql.Timestamp;

/**
 * 课程实体类
 */
public class Course {
    private int courseId;
    private String courseName;
    private String courseDescription;
    private int teacherId;
    private String teacherName;
    private Timestamp createTime;
    private boolean isActive;
    private String courseImage;
    
    public Course() {}
    
    public Course(String courseName, String courseDescription, int teacherId) {
        this.courseName = courseName;
        this.courseDescription = courseDescription;
        this.teacherId = teacherId;
        this.isActive = true;
    }
    
    // Getters and Setters
    public int getCourseId() {
        return courseId;
    }
    
    public void setCourseId(int courseId) {
        this.courseId = courseId;
    }
    
    public String getCourseName() {
        return courseName;
    }
    
    public void setCourseName(String courseName) {
        this.courseName = courseName;
    }
    
    public String getCourseDescription() {
        return courseDescription;
    }
    
    public void setCourseDescription(String courseDescription) {
        this.courseDescription = courseDescription;
    }
    
    public int getTeacherId() {
        return teacherId;
    }
    
    public void setTeacherId(int teacherId) {
        this.teacherId = teacherId;
    }
    
    public String getTeacherName() {
        return teacherName;
    }
    
    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }
    
    public Timestamp getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    public String getCourseImage() {
        return courseImage;
    }
    
    public void setCourseImage(String courseImage) {
        this.courseImage = courseImage;
    }
    
    @Override
    public String toString() {
        return "Course{" +
                "courseId=" + courseId +
                ", courseName='" + courseName + '\'' +
                ", courseDescription='" + courseDescription + '\'' +
                ", teacherId=" + teacherId +
                ", teacherName='" + teacherName + '\'' +
                ", createTime=" + createTime +
                ", isActive=" + isActive +
                ", courseImage='" + courseImage + '\'' +
                '}';
    }
}
