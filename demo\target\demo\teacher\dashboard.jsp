<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="javax.servlet.http.HttpSession" %>
<%
    // 检查用户是否已登录且为教师
    if (session.getAttribute("user") == null || !"teacher".equals(session.getAttribute("userType"))) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>教师控制台 - E-Learning平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .header {
            background: #27ae60;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logout-btn {
            background: #e74c3c;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .logout-btn:hover {
            background: #c0392b;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .card-btn {
            display: inline-block;
            background: #27ae60;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .card-btn:hover {
            background: #229954;
        }
        .card-btn.primary {
            background: #3498db;
        }
        .card-btn.primary:hover {
            background: #2980b9;
        }
        .card-btn.warning {
            background: #f39c12;
        }
        .card-btn.warning:hover {
            background: #e67e22;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #27ae60;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .welcome {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .welcome h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .welcome p {
            color: #666;
            line-height: 1.6;
        }
        .quick-actions {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .quick-actions h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .action-buttons {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        .action-btn {
            background: #3498db;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
            font-size: 14px;
        }
        .action-btn:hover {
            background: #2980b9;
        }
        .action-btn.success {
            background: #27ae60;
        }
        .action-btn.success:hover {
            background: #229954;
        }
        .action-btn.warning {
            background: #f39c12;
        }
        .action-btn.warning:hover {
            background: #e67e22;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>E-Learning 教师控制台</h1>
        <div class="user-info">
            <span>欢迎，<%= session.getAttribute("realName") %> 老师</span>
            <a href="<%= request.getContextPath() %>/logout" class="logout-btn">退出登录</a>
        </div>
    </div>

    <div class="container">
        <div class="welcome">
            <h2>欢迎来到教师控制台</h2>
            <p>在这里您可以管理您的课程、布置和批改作业、查看学生学习进度、与学生进行互动交流。让我们一起为学生创造更好的学习体验！</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">我的课程</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">待批改作业</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">学生总数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">课程资源</div>
            </div>
        </div>

        <div class="quick-actions">
            <h3>快速操作</h3>
            <div class="action-buttons">
                <a href="<%= request.getContextPath() %>/teacher/courseManagement" class="action-btn success">创建新课程</a>
                <a href="<%= request.getContextPath() %>/teacher/assignmentManagement" class="action-btn warning">布置作业</a>
                <a href="<%= request.getContextPath() %>/teacher/resourceManagement" class="action-btn">上传资源</a>
                <a href="<%= request.getContextPath() %>/teacher/studentProgress" class="action-btn">查看学生进度</a>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <h3>课程管理</h3>
                <p>创建、编辑、删除课程，管理课程内容，设置课程进度和安排。</p>
                <a href="<%= request.getContextPath() %>/teacher/courseManagement" class="card-btn">管理课程</a>
            </div>

            <div class="card">
                <h3>作业管理</h3>
                <p>布置作业、设置截止日期、批改学生作业、查看作业完成情况。</p>
                <a href="<%= request.getContextPath() %>/teacher/assignmentManagement" class="card-btn warning">作业管理</a>
            </div>

            <div class="card">
                <h3>资源管理</h3>
                <p>上传课程资源、管理教学材料、组织课程文档和多媒体内容。</p>
                <a href="<%= request.getContextPath() %>/teacher/resourceManagement" class="card-btn primary">资源管理</a>
            </div>

            <div class="card">
                <h3>学生管理</h3>
                <p>查看学生列表、监控学习进度、分析学习数据、评估学习效果。</p>
                <a href="<%= request.getContextPath() %>/teacher/studentManagement" class="card-btn">学生管理</a>
            </div>

            <div class="card">
                <h3>互动交流</h3>
                <p>参与课程讨论、回答学生问题、发布课程公告、组织在线答疑。</p>
                <a href="<%= request.getContextPath() %>/teacher/communication" class="card-btn primary">互动交流</a>
            </div>

            <div class="card">
                <h3>成绩统计</h3>
                <p>查看课程成绩统计、分析学习效果、生成成绩报告、导出数据。</p>
                <a href="<%= request.getContextPath() %>/teacher/gradeStatistics" class="card-btn warning">成绩统计</a>
            </div>
        </div>
    </div>
</body>
</html>
