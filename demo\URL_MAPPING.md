# E-Learning平台 URL映射表

## 基础访问地址
- **应用根路径**: `http://localhost:8080/demo`

## 公共页面

| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/` | 平台首页 | GET | - |
| `/index.jsp` | 平台首页 | GET | - |
| `/login.jsp` | 登录页面 | GET | - |
| `/login` | 登录处理 | POST | username, password |
| `/logout` | 退出登录 | GET/POST | - |
| `/test.jsp` | 系统测试页面 | GET | - |

## 管理员功能

### 用户管理
| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/admin/dashboard.jsp` | 管理员控制台 | GET | - |
| `/admin/userManagement` | 用户管理页面 | GET | action=delete&userId={id} |
| `/admin/userManagement` | 添加用户 | POST | action=add, username, password, realName, email, userType |

### 课程管理
| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/admin/courseManagement` | 管理员课程管理 | GET | action=delete&courseId={id} |
| `/admin/courseManagement` | 课程操作 | POST | action=add/update/delete, courseId, courseName, courseDescription |

## 教师功能

### 基础页面
| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/teacher/dashboard.jsp` | 教师控制台 | GET | - |

### 课程管理
| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/teacher/courseManagement` | 课程管理页面 | GET | action=delete&courseId={id} |
| `/teacher/courseManagement` | 课程操作 | POST | action=add/update, courseName, courseDescription |

### 作业管理
| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/teacher/assignmentManagement` | 作业管理页面 | GET | action=delete&assignmentId={id} |
| `/teacher/assignmentManagement` | 作业操作 | POST | action=add/update, title, description, courseId, dueDate |

## 学生功能

### 基础页面
| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/student/dashboard.jsp` | 学生控制台 | GET | - |

### 课程学习
| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/student/courses` | 课程列表 | GET | action=enroll&courseId={id} |
| `/student/courses` | 选课操作 | POST | action=enroll, courseId |

### 作业提交
| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/student/assignments` | 作业列表 | GET | action=view&assignmentId={id} |
| `/student/assignments` | 提交作业 | POST | action=submit, assignmentId, content, file |

## 文件操作

| URL | 描述 | 方法 | 参数 |
|-----|------|------|------|
| `/upload` | 文件上传 | POST | file, type, relatedId |
| `/download` | 文件下载 | GET | fileId, fileName |

## 错误页面

| URL | 描述 | 触发条件 |
|-----|------|----------|
| `/error/404.jsp` | 页面不存在 | HTTP 404错误 |
| `/error/500.jsp` | 服务器错误 | HTTP 500错误 |

## API接口（JSON响应）

| URL | 描述 | 方法 | 返回格式 |
|-----|------|------|----------|
| `/api/courses` | 课程列表API | GET | JSON |
| `/api/assignments` | 作业列表API | GET | JSON |
| `/api/users` | 用户列表API | GET | JSON |

## 权限控制

### 管理员权限
- `/admin/*` - 需要管理员权限
- 可以访问所有功能

### 教师权限
- `/teacher/*` - 需要教师权限
- 可以管理自己的课程和作业

### 学生权限
- `/student/*` - 需要学生权限
- 可以学习课程和提交作业

### 公共访问
- `/`, `/login.jsp`, `/login`, `/logout`, `/test.jsp` - 无需登录

## 参数说明

### 用户相关参数
- `username`: 用户名
- `password`: 密码
- `realName`: 真实姓名
- `email`: 邮箱地址
- `userType`: 用户类型 (admin/teacher/student)

### 课程相关参数
- `courseId`: 课程ID
- `courseName`: 课程名称
- `courseDescription`: 课程描述

### 作业相关参数
- `assignmentId`: 作业ID
- `title`: 作业标题
- `description`: 作业描述
- `dueDate`: 截止日期 (yyyy-MM-dd HH:mm:ss)

### 文件相关参数
- `file`: 上传的文件
- `fileId`: 文件ID
- `fileName`: 文件名
- `type`: 文件类型 (assignment/resource/avatar)
- `relatedId`: 关联ID

## 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": {}
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误信息",
  "error": "详细错误"
}
```

## 使用示例

### 登录
```
POST /demo/login
Content-Type: application/x-www-form-urlencoded

username=admin&password=admin123
```

### 添加课程
```
POST /demo/teacher/courseManagement
Content-Type: application/x-www-form-urlencoded

action=add&courseName=Java编程&courseDescription=Java基础课程
```

### 文件上传
```
POST /demo/upload
Content-Type: multipart/form-data

file=[文件数据]&type=assignment&relatedId=1
```

## 注意事项

1. 所有POST请求都需要设置正确的Content-Type
2. 文件上传使用multipart/form-data格式
3. 需要登录的页面会自动重定向到登录页面
4. 权限不足时会返回403错误
5. 所有中文内容使用UTF-8编码
