package com.elearning.entity;

import java.sql.Timestamp;

/**
 * 用户实体类
 */
public class User {
    private int userId;
    private String username;
    private String password;
    private String realName;
    private String email;
    private String userType; // admin, teacher, student
    private Timestamp createTime;
    private boolean isActive;
    
    public User() {}
    
    public User(String username, String password, String realName, String email, String userType) {
        this.username = username;
        this.password = password;
        this.realName = realName;
        this.email = email;
        this.userType = userType;
        this.isActive = true;
    }
    
    // Getters and Setters
    public int getUserId() {
        return userId;
    }
    
    public void setUserId(int userId) {
        this.userId = userId;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getRealName() {
        return realName;
    }
    
    public void setRealName(String realName) {
        this.realName = realName;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getUserType() {
        return userType;
    }
    
    public void setUserType(String userType) {
        this.userType = userType;
    }
    
    public Timestamp getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
    }
    
    @Override
    public String toString() {
        return "User{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", realName='" + realName + '\'' +
                ", email='" + email + '\'' +
                ", userType='" + userType + '\'' +
                ", createTime=" + createTime +
                ", isActive=" + isActive +
                '}';
    }
}
