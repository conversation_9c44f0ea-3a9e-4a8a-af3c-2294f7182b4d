<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.*" %>
<%@ page import="java.io.*" %>
<%@ page import="javax.servlet.*" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>配置检查 - E-Learning平台</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .check-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }
        .success {
            background: #d4edda;
            border-left-color: #28a745;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-left-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: #fff3cd;
            border-left-color: #ffc107;
            color: #856404;
        }
        .info {
            background: #d1ecf1;
            border-left-color: #17a2b8;
            color: #0c5460;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
        }
        h3 {
            color: #34495e;
            margin-top: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        th, td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .url-list {
            list-style-type: none;
            padding: 0;
        }
        .url-list li {
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .url-list a {
            color: #3498db;
            text-decoration: none;
        }
        .url-list a:hover {
            text-decoration: underline;
        }
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        .back-link a {
            color: #3498db;
            text-decoration: none;
            padding: 10px 20px;
            border: 1px solid #3498db;
            border-radius: 5px;
            transition: all 0.3s;
        }
        .back-link a:hover {
            background: #3498db;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>E-Learning 配置检查</h1>
        
        <!-- 基本信息 -->
        <div class="check-item info">
            <h3>ℹ 基本信息</h3>
            <table>
                <tr><th>项目</th><th>值</th></tr>
                <tr><td>应用名称</td><td><%= application.getServletContextName() %></td></tr>
                <tr><td>应用路径</td><td><%= request.getContextPath() %></td></tr>
                <tr><td>服务器信息</td><td><%= application.getServerInfo() %></td></tr>
                <tr><td>Java版本</td><td><%= System.getProperty("java.version") %></td></tr>
                <tr><td>字符编码</td><td><%= request.getCharacterEncoding() %></td></tr>
                <tr><td>当前时间</td><td><%= new java.util.Date() %></td></tr>
            </table>
        </div>
        
        <!-- Servlet映射检查 -->
        <div class="check-item success">
            <h3>✓ Servlet映射配置</h3>
            <p>以下是已配置的Servlet映射：</p>
            <ul class="url-list">
                <li><strong>/login</strong> - 登录处理 (LoginServlet)</li>
                <li><strong>/logout</strong> - 退出登录 (LogoutServlet)</li>
                <li><strong>/admin/userManagement</strong> - 用户管理 (UserManagementServlet)</li>
                <li><strong>/admin/courseManagement</strong> - 管理员课程管理 (AdminCourseManagementServlet)</li>
                <li><strong>/teacher/courseManagement</strong> - 教师课程管理 (CourseManagementServlet)</li>
                <li><strong>/teacher/assignmentManagement</strong> - 作业管理 (AssignmentManagementServlet)</li>
                <li><strong>/student/courses</strong> - 学生课程 (StudentCourseServlet)</li>
                <li><strong>/student/assignments</strong> - 学生作业 (StudentAssignmentServlet)</li>
                <li><strong>/upload</strong> - 文件上传 (FileUploadServlet)</li>
                <li><strong>/download</strong> - 文件下载 (FileDownloadServlet)</li>
            </ul>
        </div>
        
        <!-- 页面访问测试 -->
        <div class="check-item info">
            <h3>ℹ 页面访问测试</h3>
            <p>点击以下链接测试页面访问：</p>
            <ul class="url-list">
                <li><a href="<%= request.getContextPath() %>/" target="_blank">首页</a></li>
                <li><a href="<%= request.getContextPath() %>/login.jsp" target="_blank">登录页面</a></li>
                <li><a href="<%= request.getContextPath() %>/test.jsp" target="_blank">系统测试页面</a></li>
                <li><a href="<%= request.getContextPath() %>/admin/dashboard.jsp" target="_blank">管理员控制台</a></li>
                <li><a href="<%= request.getContextPath() %>/teacher/dashboard.jsp" target="_blank">教师控制台</a></li>
                <li><a href="<%= request.getContextPath() %>/student/dashboard.jsp" target="_blank">学生控制台</a></li>
            </ul>
        </div>
        
        <!-- 上下文参数 -->
        <div class="check-item info">
            <h3>ℹ 上下文参数</h3>
            <table>
                <tr><th>参数名</th><th>参数值</th></tr>
                <%
                    Enumeration<String> paramNames = application.getInitParameterNames();
                    while (paramNames.hasMoreElements()) {
                        String paramName = paramNames.nextElement();
                        String paramValue = application.getInitParameter(paramName);
                %>
                <tr><td><%= paramName %></td><td><%= paramValue %></td></tr>
                <%
                    }
                %>
            </table>
        </div>
        
        <!-- Session信息 -->
        <div class="check-item info">
            <h3>ℹ Session信息</h3>
            <table>
                <tr><th>属性</th><th>值</th></tr>
                <tr><td>Session ID</td><td><%= session.getId() %></td></tr>
                <tr><td>创建时间</td><td><%= new java.util.Date(session.getCreationTime()) %></td></tr>
                <tr><td>最后访问时间</td><td><%= new java.util.Date(session.getLastAccessedTime()) %></td></tr>
                <tr><td>最大非活动间隔</td><td><%= session.getMaxInactiveInterval() %> 秒</td></tr>
                <tr><td>是否新Session</td><td><%= session.isNew() %></td></tr>
            </table>
            
            <% if (session.getAttribute("user") != null) { %>
                <h4>当前登录用户信息：</h4>
                <table>
                    <tr><td>用户名</td><td><%= session.getAttribute("username") %></td></tr>
                    <tr><td>真实姓名</td><td><%= session.getAttribute("realName") %></td></tr>
                    <tr><td>用户类型</td><td><%= session.getAttribute("userType") %></td></tr>
                    <tr><td>用户ID</td><td><%= session.getAttribute("userId") %></td></tr>
                </table>
            <% } else { %>
                <p><strong>当前未登录</strong></p>
            <% } %>
        </div>
        
        <!-- 文件上传配置 -->
        <div class="check-item success">
            <h3>✓ 文件上传配置</h3>
            <table>
                <tr><th>配置项</th><th>值</th></tr>
                <tr><td>最大文件大小</td><td>50MB</td></tr>
                <tr><td>最大请求大小</td><td>100MB</td></tr>
                <tr><td>文件大小阈值</td><td>1MB</td></tr>
                <tr><td>上传目录</td><td>/uploads</td></tr>
            </table>
        </div>
        
        <!-- MIME类型配置 -->
        <div class="check-item success">
            <h3>✓ MIME类型配置</h3>
            <p>已配置的文件类型支持：</p>
            <ul>
                <li><strong>文档类型：</strong> PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX</li>
                <li><strong>压缩文件：</strong> ZIP, RAR</li>
                <li><strong>多媒体：</strong> MP4, MP3</li>
                <li><strong>Web文件：</strong> CSS, JS</li>
            </ul>
        </div>
        
        <!-- 安全配置 -->
        <div class="check-item warning">
            <h3>⚠ 安全配置</h3>
            <p>已配置的安全约束：</p>
            <ul>
                <li><strong>/admin/*</strong> - 管理员区域保护</li>
                <li><strong>/teacher/*</strong> - 教师区域保护</li>
                <li><strong>/student/*</strong> - 学生区域保护</li>
            </ul>
            <p><strong>注意：</strong> 当前安全配置仅为声明性配置，实际权限控制在应用层实现。</p>
        </div>
        
        <!-- 快速测试 -->
        <div class="check-item info">
            <h3>ℹ 快速测试建议</h3>
            <ol>
                <li>访问 <a href="<%= request.getContextPath() %>/test.jsp" target="_blank">系统测试页面</a> 检查数据库连接</li>
                <li>使用测试账号登录：
                    <ul>
                        <li>管理员：admin / admin123</li>
                        <li>教师：teacher / teacher123</li>
                        <li>学生：student / student123</li>
                    </ul>
                </li>
                <li>测试各个功能模块的访问权限</li>
                <li>检查文件上传下载功能</li>
                <li>验证字符编码是否正确</li>
            </ol>
        </div>
        
        <div class="back-link">
            <a href="<%= request.getContextPath() %>/">返回首页</a>
            <a href="<%= request.getContextPath() %>/test.jsp">系统测试</a>
        </div>
    </div>
</body>
</html>
