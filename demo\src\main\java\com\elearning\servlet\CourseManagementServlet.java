package com.elearning.servlet;

import com.elearning.dao.CourseDAO;
import com.elearning.entity.Course;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.util.List;

/**
 * 课程管理Servlet
 */
@WebServlet("/teacher/courseManagement")
public class CourseManagementServlet extends HttpServlet {
    private CourseDAO courseDAO = new CourseDAO();
    
    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // 检查教师权限
        if (!checkTeacherPermission(request, response)) {
            return;
        }
        
        String action = request.getParameter("action");
        
        if ("delete".equals(action)) {
            deleteCourse(request, response);
        } else {
            // 默认显示课程列表
            showCourseList(request, response);
        }
    }
    
    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        // 检查教师权限
        if (!checkTeacherPermission(request, response)) {
            return;
        }
        
        request.setCharacterEncoding("UTF-8");
        
        String action = request.getParameter("action");
        
        if ("add".equals(action)) {
            addCourse(request, response);
        } else if ("update".equals(action)) {
            updateCourse(request, response);
        }
    }
    
    /**
     * 检查教师权限
     */
    private boolean checkTeacherPermission(HttpServletRequest request, HttpServletResponse response) 
            throws IOException {
        HttpSession session = request.getSession();
        String userType = (String) session.getAttribute("userType");
        
        if (!"teacher".equals(userType)) {
            response.sendRedirect(request.getContextPath() + "/login.jsp");
            return false;
        }
        return true;
    }
    
    /**
     * 显示课程列表
     */
    private void showCourseList(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        HttpSession session = request.getSession();
        Integer teacherId = (Integer) session.getAttribute("userId");
        
        List<Course> courses = courseDAO.getCoursesByTeacherId(teacherId);
        request.setAttribute("courses", courses);
        request.getRequestDispatcher("/teacher/courseManagement.jsp").forward(request, response);
    }
    
    /**
     * 添加课程
     */
    private void addCourse(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String courseName = request.getParameter("courseName");
        String courseDescription = request.getParameter("courseDescription");
        
        HttpSession session = request.getSession();
        Integer teacherId = (Integer) session.getAttribute("userId");
        
        // 验证输入
        if (courseName == null || courseName.trim().isEmpty()) {
            request.setAttribute("error", "课程名称不能为空");
            showCourseList(request, response);
            return;
        }
        
        // 创建课程对象
        Course course = new Course(courseName.trim(), courseDescription, teacherId);
        
        // 添加课程
        if (courseDAO.addCourse(course)) {
            request.setAttribute("success", "课程添加成功");
        } else {
            request.setAttribute("error", "课程添加失败");
        }
        
        showCourseList(request, response);
    }
    
    /**
     * 更新课程
     */
    private void updateCourse(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String courseIdStr = request.getParameter("courseId");
        String courseName = request.getParameter("courseName");
        String courseDescription = request.getParameter("courseDescription");
        
        try {
            int courseId = Integer.parseInt(courseIdStr);
            
            // 验证输入
            if (courseName == null || courseName.trim().isEmpty()) {
                request.setAttribute("error", "课程名称不能为空");
                showCourseList(request, response);
                return;
            }
            
            // 创建课程对象
            Course course = new Course();
            course.setCourseId(courseId);
            course.setCourseName(courseName.trim());
            course.setCourseDescription(courseDescription);
            
            // 更新课程
            if (courseDAO.updateCourse(course)) {
                request.setAttribute("success", "课程更新成功");
            } else {
                request.setAttribute("error", "课程更新失败");
            }
        } catch (NumberFormatException e) {
            request.setAttribute("error", "无效的课程ID");
        }
        
        showCourseList(request, response);
    }
    
    /**
     * 删除课程
     */
    private void deleteCourse(HttpServletRequest request, HttpServletResponse response) 
            throws ServletException, IOException {
        
        String courseIdStr = request.getParameter("courseId");
        
        try {
            int courseId = Integer.parseInt(courseIdStr);
            
            if (courseDAO.deleteCourse(courseId)) {
                request.setAttribute("success", "课程删除成功");
            } else {
                request.setAttribute("error", "课程删除失败");
            }
        } catch (NumberFormatException e) {
            request.setAttribute("error", "无效的课程ID");
        }
        
        showCourseList(request, response);
    }
}
