<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://xmlns.jcp.org/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee
         http://xmlns.jcp.org/xml/ns/javaee/web-app_4_0.xsd"
         version="4.0">

  <display-name>E-Learning Platform</display-name>

  <!-- 设置默认编码 -->
  <filter>
    <filter-name>CharacterEncodingFilter</filter-name>
    <filter-class>com.elearning.filter.CharacterEncodingFilter</filter-class>
    <init-param>
      <param-name>encoding</param-name>
      <param-value>UTF-8</param-value>
    </init-param>
  </filter>

  <filter-mapping>
    <filter-name>CharacterEncodingFilter</filter-name>
    <url-pattern>/*</url-pattern>
  </filter-mapping>

  <!-- 添加登录验证过滤器 -->
  <filter>
    <filter-name>AuthenticationFilter</filter-name>
    <filter-class>com.elearning.filter.AuthenticationFilter</filter-class>
  </filter>

  <filter-mapping>
    <filter-name>AuthenticationFilter</filter-name>
    <url-pattern>/admin/*</url-pattern>
  </filter-mapping>

  <filter-mapping>
    <filter-name>AuthenticationFilter</filter-name>
    <url-pattern>/teacher/*</url-pattern>
  </filter-mapping>

  <filter-mapping>
    <filter-name>AuthenticationFilter</filter-name>
    <url-pattern>/student/*</url-pattern>
  </filter-mapping>

  <!-- Servlet配置 -->
  <servlet>
    <servlet-name>LoginServlet</servlet-name>
    <servlet-class>com.elearning.servlet.LoginServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>LoginServlet</servlet-name>
    <url-pattern>/login</url-pattern>
  </servlet-mapping>

  <servlet>
    <servlet-name>LogoutServlet</servlet-name>
    <servlet-class>com.elearning.servlet.LogoutServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>LogoutServlet</servlet-name>
    <url-pattern>/logout</url-pattern>
  </servlet-mapping>

  <servlet>
    <servlet-name>UserManagementServlet</servlet-name>
    <servlet-class>com.elearning.servlet.UserManagementServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>UserManagementServlet</servlet-name>
    <url-pattern>/admin/userManagement</url-pattern>
  </servlet-mapping>

  <servlet>
    <servlet-name>CourseManagementServlet</servlet-name>
    <servlet-class>com.elearning.servlet.CourseManagementServlet</servlet-class>
    <!-- 正确的文件上传配置位置 -->
    <multipart-config>
      <max-file-size>52428800</max-file-size>  <!-- 50MB -->
      <max-request-size>104857600</max-request-size>  <!-- 100MB -->
      <file-size-threshold>1048576</file-size-threshold>  <!-- 1MB -->
    </multipart-config>
  </servlet>

  <servlet-mapping>
    <servlet-name>CourseManagementServlet</servlet-name>
    <url-pattern>/teacher/courseManagement</url-pattern>
  </servlet-mapping>

  <!-- 作业管理Servlet -->
  <servlet>
    <servlet-name>AssignmentManagementServlet</servlet-name>
    <servlet-class>com.elearning.servlet.AssignmentManagementServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>AssignmentManagementServlet</servlet-name>
    <url-pattern>/teacher/assignmentManagement</url-pattern>
  </servlet-mapping>

  <!-- 学生课程Servlet -->
  <servlet>
    <servlet-name>StudentCourseServlet</servlet-name>
    <servlet-class>com.elearning.servlet.StudentCourseServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>StudentCourseServlet</servlet-name>
    <url-pattern>/student/courses</url-pattern>
  </servlet-mapping>

  <!-- 学生作业Servlet -->
  <servlet>
    <servlet-name>StudentAssignmentServlet</servlet-name>
    <servlet-class>com.elearning.servlet.StudentAssignmentServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>StudentAssignmentServlet</servlet-name>
    <url-pattern>/student/assignments</url-pattern>
  </servlet-mapping>

  <!-- 管理员课程管理Servlet -->
  <servlet>
    <servlet-name>AdminCourseManagementServlet</servlet-name>
    <servlet-class>com.elearning.servlet.AdminCourseManagementServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>AdminCourseManagementServlet</servlet-name>
    <url-pattern>/admin/courseManagement</url-pattern>
  </servlet-mapping>

  <!-- 文件上传Servlet -->
  <servlet>
    <servlet-name>FileUploadServlet</servlet-name>
    <servlet-class>com.elearning.servlet.FileUploadServlet</servlet-class>
    <!-- 正确的文件上传配置位置 -->
    <multipart-config>
      <max-file-size>52428800</max-file-size>  <!-- 50MB -->
      <max-request-size>104857600</max-request-size>  <!-- 100MB -->
      <file-size-threshold>1048576</file-size-threshold>  <!-- 1MB -->
    </multipart-config>
  </servlet>

  <servlet-mapping>
    <servlet-name>FileUploadServlet</servlet-name>
    <url-pattern>/upload</url-pattern>
  </servlet-mapping>

  <!-- 文件下载Servlet -->
  <servlet>
    <servlet-name>FileDownloadServlet</servlet-name>
    <servlet-class>com.elearning.servlet.FileDownloadServlet</servlet-class>
  </servlet>

  <servlet-mapping>
    <servlet-name>FileDownloadServlet</servlet-name>
    <url-pattern>/download</url-pattern>
  </servlet-mapping>

  <!-- 欢迎页面 -->
  <welcome-file-list>
    <welcome-file>index.jsp</welcome-file>
  </welcome-file-list>

  <!-- Session超时设置（30分钟） -->
  <session-config>
    <session-timeout>30</session-timeout>
  </session-config>

  <!-- 错误页面配置 -->
  <error-page>
    <error-code>404</error-code>
    <location>/error/404.jsp</location>
  </error-page>

  <error-page>
    <error-code>500</error-code>
    <location>/error/500.jsp</location>
  </error-page>

  <!-- 完善安全约束配置 -->
  <security-constraint>
    <web-resource-collection>
      <web-resource-name>Admin Area</web-resource-name>
      <url-pattern>/admin/*</url-pattern>
      <http-method>GET</http-method>
      <http-method>POST</http-method>
    </web-resource-collection>
    <auth-constraint>
      <role-name>admin</role-name>
    </auth-constraint>
    <user-data-constraint>
      <transport-guarantee>NONE</transport-guarantee>
    </user-data-constraint>
  </security-constraint>

  <security-constraint>
    <web-resource-collection>
      <web-resource-name>Teacher Area</web-resource-name>
      <url-pattern>/teacher/*</url-pattern>
      <http-method>GET</http-method>
      <http-method>POST</http-method>
    </web-resource-collection>
    <auth-constraint>
      <role-name>teacher</role-name>
    </auth-constraint>
    <user-data-constraint>
      <transport-guarantee>NONE</transport-guarantee>
    </user-data-constraint>
  </security-constraint>

  <security-constraint>
    <web-resource-collection>
      <web-resource-name>Student Area</web-resource-name>
      <url-pattern>/student/*</url-pattern>
      <http-method>GET</http-method>
      <http-method>POST</http-method>
    </web-resource-collection>
    <auth-constraint>
      <role-name>student</role-name>
    </auth-constraint>
    <user-data-constraint>
      <transport-guarantee>NONE</transport-guarantee>
    </user-data-constraint>
  </security-constraint>

  <!-- 登录配置 -->
  <login-config>
    <auth-method>FORM</auth-method>
    <realm-name>E-Learning Security Realm</realm-name>
    <form-login-config>
      <form-login-page>/login.jsp</form-login-page>
      <form-error-page>/loginError.jsp</form-error-page>
    </form-login-config>
  </login-config>

  <!-- 定义安全角色 -->
  <security-role>
    <role-name>admin</role-name>
  </security-role>
  <security-role>
    <role-name>teacher</role-name>
  </security-role>
  <security-role>
    <role-name>student</role-name>
  </security-role>

  <!-- MIME类型配置 -->
  <mime-mapping>
    <extension>css</extension>
    <mime-type>text/css</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>js</extension>
    <mime-type>application/javascript</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>pdf</extension>
    <mime-type>application/pdf</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>doc</extension>
    <mime-type>application/msword</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>docx</extension>
    <mime-type>application/vnd.openxmlformats-officedocument.wordprocessingml.document</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>ppt</extension>
    <mime-type>application/vnd.ms-powerpoint</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>pptx</extension>
    <mime-type>application/vnd.openxmlformats-officedocument.presentationml.presentation</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>xls</extension>
    <mime-type>application/vnd.ms-excel</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>xlsx</extension>
    <mime-type>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>zip</extension>
    <mime-type>application/zip</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>rar</extension>
    <mime-type>application/x-rar-compressed</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>mp4</extension>
    <mime-type>video/mp4</mime-type>
  </mime-mapping>

  <mime-mapping>
    <extension>mp3</extension>
    <mime-type>audio/mpeg</mime-type>
  </mime-mapping>

  <!-- 上下文参数 -->
  <context-param>
    <param-name>upload.directory</param-name>
    <param-value>/uploads</param-value>
  </context-param>

  <context-param>
    <param-name>max.file.size</param-name>
    <param-value>52428800</param-value>
  </context-param>

  <!-- 添加Session监听器 -->
  <listener>
    <listener-class>com.elearning.listener.SessionListener</listener-class>
  </listener>

</web-app>