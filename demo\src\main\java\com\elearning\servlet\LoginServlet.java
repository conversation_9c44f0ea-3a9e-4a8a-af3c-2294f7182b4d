package com.elearning.servlet;

import com.elearning.dao.UserDAO;
import com.elearning.entity.User;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * 登录处理Servlet
 */
@WebServlet("/login")
public class LoginServlet extends HttpServlet {
    private UserDAO userDAO = new UserDAO();

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {
        // 转发到登录页面
        request.getRequestDispatcher("/login.jsp").forward(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        request.setCharacterEncoding("UTF-8");
        response.setCharacterEncoding("UTF-8");
        response.setContentType("text/html; charset=UTF-8");

        String username = request.getParameter("username");
        String password = request.getParameter("password");

        System.out.println("登录请求 - 用户名: " + username);

        // 验证输入
        if (username == null || username.trim().isEmpty() ||
            password == null || password.trim().isEmpty()) {
            System.out.println("登录失败: 用户名或密码为空");
            request.setAttribute("error", "用户名和密码不能为空");
            request.getRequestDispatcher("/login.jsp").forward(request, response);
            return;
        }

        // 验证用户
        User user = userDAO.login(username.trim(), password);

        if (user != null) {
            System.out.println("登录成功 - 用户: " + user.getUsername() + ", 类型: " + user.getUserType());

            // 登录成功，创建session
            HttpSession session = request.getSession();
            session.setAttribute("user", user);
            session.setAttribute("userId", user.getUserId());
            session.setAttribute("username", user.getUsername());
            session.setAttribute("userType", user.getUserType());
            session.setAttribute("realName", user.getRealName());

            // 根据用户类型跳转到不同页面
            String redirectUrl = "";
            switch (user.getUserType()) {
                case "admin":
                    redirectUrl = request.getContextPath() + "/admin/dashboard.jsp";
                    break;
                case "teacher":
                    redirectUrl = request.getContextPath() + "/teacher/dashboard.jsp";
                    break;
                case "student":
                    redirectUrl = request.getContextPath() + "/student/dashboard.jsp";
                    break;
                default:
                    redirectUrl = request.getContextPath() + "/login.jsp";
                    break;
            }

            System.out.println("重定向到: " + redirectUrl);
            response.sendRedirect(redirectUrl);
        } else {
            // 登录失败
            System.out.println("登录失败 - 用户名或密码错误");
            request.setAttribute("error", "用户名或密码错误");
            request.getRequestDispatcher("/login.jsp").forward(request, response);
        }
    }
}
