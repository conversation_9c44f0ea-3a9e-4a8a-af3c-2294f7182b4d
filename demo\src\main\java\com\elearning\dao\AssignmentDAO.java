package com.elearning.dao;

import com.elearning.entity.Assignment;
import com.elearning.util.DatabaseConnection;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;

/**
 * 作业数据访问对象
 */
public class AssignmentDAO {
    
    /**
     * 添加作业
     */
    public boolean addAssignment(Assignment assignment) {
        String sql = "INSERT INTO assignments (title, description, course_id, teacher_id, due_date, attachment_path, create_time, is_active) VALUES (?, ?, ?, ?, ?, ?, NOW(), 1)";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, assignment.getTitle());
            pstmt.setString(2, assignment.getDescription());
            pstmt.setInt(3, assignment.getCourseId());
            pstmt.setInt(4, assignment.getTeacherId());
            pstmt.setTimestamp(5, assignment.getDueDate());
            pstmt.setString(6, assignment.getAttachmentPath());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 根据教师ID获取作业列表
     */
    public List<Assignment> getAssignmentsByTeacherId(int teacherId) {
        List<Assignment> assignments = new ArrayList<>();
        String sql = "SELECT a.*, c.course_name FROM assignments a " +
                    "LEFT JOIN courses c ON a.course_id = c.course_id " +
                    "WHERE a.teacher_id = ? AND a.is_active = 1 " +
                    "ORDER BY a.create_time DESC";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, teacherId);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                Assignment assignment = new Assignment();
                assignment.setAssignmentId(rs.getInt("assignment_id"));
                assignment.setTitle(rs.getString("title"));
                assignment.setDescription(rs.getString("description"));
                assignment.setCourseId(rs.getInt("course_id"));
                assignment.setCourseName(rs.getString("course_name"));
                assignment.setTeacherId(rs.getInt("teacher_id"));
                assignment.setCreateTime(rs.getTimestamp("create_time"));
                assignment.setDueDate(rs.getTimestamp("due_date"));
                assignment.setAttachmentPath(rs.getString("attachment_path"));
                assignment.setActive(rs.getBoolean("is_active"));
                assignments.add(assignment);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return assignments;
    }
    
    /**
     * 根据课程ID获取作业列表
     */
    public List<Assignment> getAssignmentsByCourseId(int courseId) {
        List<Assignment> assignments = new ArrayList<>();
        String sql = "SELECT a.*, c.course_name FROM assignments a " +
                    "LEFT JOIN courses c ON a.course_id = c.course_id " +
                    "WHERE a.course_id = ? AND a.is_active = 1 " +
                    "ORDER BY a.due_date ASC";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, courseId);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                Assignment assignment = new Assignment();
                assignment.setAssignmentId(rs.getInt("assignment_id"));
                assignment.setTitle(rs.getString("title"));
                assignment.setDescription(rs.getString("description"));
                assignment.setCourseId(rs.getInt("course_id"));
                assignment.setCourseName(rs.getString("course_name"));
                assignment.setTeacherId(rs.getInt("teacher_id"));
                assignment.setCreateTime(rs.getTimestamp("create_time"));
                assignment.setDueDate(rs.getTimestamp("due_date"));
                assignment.setAttachmentPath(rs.getString("attachment_path"));
                assignment.setActive(rs.getBoolean("is_active"));
                assignments.add(assignment);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return assignments;
    }
    
    /**
     * 根据ID获取作业
     */
    public Assignment getAssignmentById(int assignmentId) {
        String sql = "SELECT a.*, c.course_name FROM assignments a " +
                    "LEFT JOIN courses c ON a.course_id = c.course_id " +
                    "WHERE a.assignment_id = ? AND a.is_active = 1";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, assignmentId);
            ResultSet rs = pstmt.executeQuery();
            
            if (rs.next()) {
                Assignment assignment = new Assignment();
                assignment.setAssignmentId(rs.getInt("assignment_id"));
                assignment.setTitle(rs.getString("title"));
                assignment.setDescription(rs.getString("description"));
                assignment.setCourseId(rs.getInt("course_id"));
                assignment.setCourseName(rs.getString("course_name"));
                assignment.setTeacherId(rs.getInt("teacher_id"));
                assignment.setCreateTime(rs.getTimestamp("create_time"));
                assignment.setDueDate(rs.getTimestamp("due_date"));
                assignment.setAttachmentPath(rs.getString("attachment_path"));
                assignment.setActive(rs.getBoolean("is_active"));
                return assignment;
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return null;
    }
    
    /**
     * 更新作业
     */
    public boolean updateAssignment(Assignment assignment) {
        String sql = "UPDATE assignments SET title = ?, description = ?, due_date = ? WHERE assignment_id = ?";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setString(1, assignment.getTitle());
            pstmt.setString(2, assignment.getDescription());
            pstmt.setTimestamp(3, assignment.getDueDate());
            pstmt.setInt(4, assignment.getAssignmentId());
            
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 删除作业（软删除）
     */
    public boolean deleteAssignment(int assignmentId) {
        String sql = "UPDATE assignments SET is_active = 0 WHERE assignment_id = ?";
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, assignmentId);
            return pstmt.executeUpdate() > 0;
        } catch (SQLException e) {
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取学生的作业列表（通过选课关系）
     */
    public List<Assignment> getAssignmentsForStudent(int studentId) {
        List<Assignment> assignments = new ArrayList<>();
        String sql = "SELECT DISTINCT a.*, c.course_name FROM assignments a " +
                    "LEFT JOIN courses c ON a.course_id = c.course_id " +
                    "LEFT JOIN enrollments e ON c.course_id = e.course_id " +
                    "WHERE e.student_id = ? AND a.is_active = 1 AND e.is_active = 1 " +
                    "ORDER BY a.due_date ASC";
        
        try (Connection conn = DatabaseConnection.getConnection();
             PreparedStatement pstmt = conn.prepareStatement(sql)) {
            
            pstmt.setInt(1, studentId);
            ResultSet rs = pstmt.executeQuery();
            
            while (rs.next()) {
                Assignment assignment = new Assignment();
                assignment.setAssignmentId(rs.getInt("assignment_id"));
                assignment.setTitle(rs.getString("title"));
                assignment.setDescription(rs.getString("description"));
                assignment.setCourseId(rs.getInt("course_id"));
                assignment.setCourseName(rs.getString("course_name"));
                assignment.setTeacherId(rs.getInt("teacher_id"));
                assignment.setCreateTime(rs.getTimestamp("create_time"));
                assignment.setDueDate(rs.getTimestamp("due_date"));
                assignment.setAttachmentPath(rs.getString("attachment_path"));
                assignment.setActive(rs.getBoolean("is_active"));
                assignments.add(assignment);
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
        return assignments;
    }
}
