# E-Learning平台部署指南

## 快速部署步骤

### 1. 环境准备

确保您的系统已安装以下软件：
- **JDK 1.8+** (推荐使用JDK 11)
- **MySQL 8.0+**
- **Apache Tomcat 9.0+**
- **Maven 3.6+** (用于构建项目)

### 2. 数据库配置

#### 2.1 创建数据库
```sql
-- 登录MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE elearning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 退出MySQL
exit;
```

#### 2.2 导入数据库结构和初始数据
```bash
# 在项目根目录执行
mysql -u root -p elearning < database/elearning.sql
```

#### 2.3 修改数据库连接配置（如果需要）
编辑文件：`src/main/java/com/elearning/util/DatabaseConnection.java`

```java
// 修改以下配置为您的数据库信息
private static final String URL = "********************************************************************************************";
private static final String USERNAME = "root";  // 您的数据库用户名
private static final String PASSWORD = "123456"; // 您的数据库密码
```

### 3. 项目构建

#### 3.1 编译项目
```bash
# 进入项目目录
cd demo

# 清理并编译
mvn clean compile

# 打包项目
mvn package
```

#### 3.2 验证构建结果
构建成功后，会在 `target` 目录下生成 `demo.war` 文件。

### 4. 部署到Tomcat

#### 4.1 方法一：直接部署WAR文件
1. 将 `target/demo.war` 复制到Tomcat的 `webapps` 目录
2. 启动Tomcat服务器
3. Tomcat会自动解压WAR文件

#### 4.2 方法二：使用Tomcat Manager部署
1. 访问Tomcat Manager界面
2. 上传 `demo.war` 文件
3. 点击部署

#### 4.3 启动Tomcat
```bash
# Windows
%CATALINA_HOME%\bin\startup.bat

# Linux/Mac
$CATALINA_HOME/bin/startup.sh
```

### 5. 访问应用

部署成功后，通过以下URL访问应用：
- **应用首页**: http://localhost:8080/demo
- **登录页面**: http://localhost:8080/demo/login.jsp

### 6. 测试账号

使用以下预置账号进行测试：

| 角色 | 用户名 | 密码 | 功能 |
|------|--------|------|------|
| 管理员 | admin | admin123 | 用户管理、系统管理 |
| 教师 | teacher | teacher123 | 课程管理、作业管理 |
| 学生 | student | student123 | 课程学习、作业提交 |

## 常见问题解决

### 1. 数据库连接失败
**问题**: 应用启动后无法连接数据库

**解决方案**:
- 检查MySQL服务是否启动
- 确认数据库连接参数是否正确
- 检查防火墙设置
- 验证数据库用户权限

### 2. 页面显示乱码
**问题**: 中文字符显示为乱码

**解决方案**:
- 确保数据库字符集为utf8mb4
- 检查Tomcat的server.xml配置：
```xml
<Connector port="8080" protocol="HTTP/1.1"
           connectionTimeout="20000"
           redirectPort="8443"
           URIEncoding="UTF-8" />
```

### 3. 404错误
**问题**: 访问页面时出现404错误

**解决方案**:
- 确认WAR文件已正确部署
- 检查URL路径是否正确
- 查看Tomcat日志文件

### 4. 500内部服务器错误
**问题**: 页面访问时出现500错误

**解决方案**:
- 查看Tomcat日志文件定位具体错误
- 检查数据库连接是否正常
- 确认所有依赖JAR包已正确加载

## 生产环境部署建议

### 1. 安全配置
- 修改默认账号密码
- 配置HTTPS
- 设置防火墙规则
- 定期备份数据库

### 2. 性能优化
- 配置数据库连接池
- 启用Tomcat的压缩功能
- 配置静态资源缓存
- 监控系统性能

### 3. 日志配置
- 配置应用日志级别
- 设置日志文件轮转
- 监控错误日志

### 4. 备份策略
- 定期备份数据库
- 备份应用配置文件
- 制定灾难恢复计划

## 技术支持

如果在部署过程中遇到问题，请：
1. 查看Tomcat日志文件
2. 检查数据库连接状态
3. 验证环境配置是否正确
4. 参考项目README.md文档

## 版本信息

- **项目版本**: 1.0-SNAPSHOT
- **Java版本**: 1.8+
- **Tomcat版本**: 9.0+
- **MySQL版本**: 8.0+
- **Maven版本**: 3.6+
