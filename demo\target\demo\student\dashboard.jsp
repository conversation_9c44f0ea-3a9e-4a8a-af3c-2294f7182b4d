<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="javax.servlet.http.HttpSession" %>
<%
    // 检查用户是否已登录且为学生
    if (session.getAttribute("user") == null || !"student".equals(session.getAttribute("userType"))) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>学生控制台 - E-Learning平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .header {
            background: #3498db;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logout-btn {
            background: #e74c3c;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .logout-btn:hover {
            background: #c0392b;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .card-btn {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .card-btn:hover {
            background: #2980b9;
        }
        .card-btn.success {
            background: #27ae60;
        }
        .card-btn.success:hover {
            background: #229954;
        }
        .card-btn.warning {
            background: #f39c12;
        }
        .card-btn.warning:hover {
            background: #e67e22;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .welcome {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .welcome h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .welcome p {
            color: #666;
            line-height: 1.6;
        }
        .recent-activities {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .recent-activities h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .activity-item {
            padding: 15px;
            border-left: 4px solid #3498db;
            background: #f8f9fa;
            margin-bottom: 10px;
            border-radius: 0 5px 5px 0;
        }
        .activity-item h4 {
            color: #2c3e50;
            margin-bottom: 5px;
        }
        .activity-item p {
            color: #666;
            font-size: 14px;
        }
        .progress-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .progress-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .progress-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border: 1px solid #eee;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        .progress-bar {
            width: 200px;
            height: 10px;
            background: #eee;
            border-radius: 5px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #27ae60;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>E-Learning 学生控制台</h1>
        <div class="user-info">
            <span>欢迎，<%= session.getAttribute("realName") %> 同学</span>
            <a href="<%= request.getContextPath() %>/logout" class="logout-btn">退出登录</a>
        </div>
    </div>

    <div class="container">
        <div class="welcome">
            <h2>欢迎来到学习平台</h2>
            <p>在这里您可以浏览和学习课程、完成作业、查看学习进度、与老师和同学进行交流。让我们一起开启精彩的学习之旅！</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">已选课程</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">待完成作业</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0%</div>
                <div class="stat-label">平均进度</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">学习时长(小时)</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <h3>课程学习</h3>
                <p>浏览可选课程，观看课程视频，阅读学习资料，跟踪学习进度。</p>
                <a href="<%= request.getContextPath() %>/student/courses" class="card-btn">开始学习</a>
            </div>

            <div class="card">
                <h3>作业中心</h3>
                <p>查看待完成作业，提交作业文件，查看作业成绩和老师反馈。</p>
                <a href="<%= request.getContextPath() %>/student/assignments" class="card-btn warning">作业中心</a>
            </div>

            <div class="card">
                <h3>学习进度</h3>
                <p>查看各课程学习进度，分析学习数据，制定学习计划。</p>
                <a href="<%= request.getContextPath() %>/student/progress" class="card-btn success">学习进度</a>
            </div>

            <div class="card">
                <h3>互动交流</h3>
                <p>参与课程讨论，向老师提问，与同学交流学习心得。</p>
                <a href="<%= request.getContextPath() %>/student/communication" class="card-btn">互动交流</a>
            </div>

            <div class="card">
                <h3>成绩查询</h3>
                <p>查看考试成绩，作业评分，课程总评，下载成绩单。</p>
                <a href="<%= request.getContextPath() %>/student/grades" class="card-btn success">成绩查询</a>
            </div>

            <div class="card">
                <h3>个人资料</h3>
                <p>修改个人信息，更新联系方式，设置学习偏好。</p>
                <a href="<%= request.getContextPath() %>/student/profile" class="card-btn warning">个人资料</a>
            </div>
        </div>

        <div class="recent-activities">
            <h3>最近活动</h3>
            <div class="activity-item">
                <h4>欢迎加入E-Learning平台</h4>
                <p>开始您的学习之旅，探索丰富的课程资源</p>
            </div>
        </div>

        <div class="progress-section">
            <h3>课程进度</h3>
            <div style="text-align: center; color: #666; padding: 20px;">
                暂无课程数据，请先选择课程开始学习
            </div>
        </div>
    </div>
</body>
</html>
