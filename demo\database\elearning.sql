-- E-Learning平台数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS elearning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE elearning;

-- 用户表
CREATE TABLE users (
    user_id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    real_name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    user_type ENUM('admin', 'teacher', 'student') NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    INDEX idx_username (username),
    INDEX idx_user_type (user_type)
);

-- 课程表
CREATE TABLE courses (
    course_id INT AUTO_INCREMENT PRIMARY KEY,
    course_name VARCHAR(200) NOT NULL,
    course_description TEXT,
    teacher_id INT NOT NULL,
    course_image VARCHAR(255),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (teacher_id) REFERENCES users(user_id),
    INDEX idx_teacher_id (teacher_id)
);

-- 课程资源表
CREATE TABLE resources (
    resource_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    resource_name VARCHAR(200) NOT NULL,
    resource_type ENUM('video', 'document', 'image', 'audio', 'other') NOT NULL,
    resource_path VARCHAR(500) NOT NULL,
    resource_size BIGINT,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (course_id) REFERENCES courses(course_id),
    INDEX idx_course_id (course_id)
);

-- 作业表
CREATE TABLE assignments (
    assignment_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    course_id INT NOT NULL,
    teacher_id INT NOT NULL,
    attachment_path VARCHAR(500),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    due_date TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (course_id) REFERENCES courses(course_id),
    FOREIGN KEY (teacher_id) REFERENCES users(user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_teacher_id (teacher_id),
    INDEX idx_due_date (due_date)
);

-- 作业提交表
CREATE TABLE submissions (
    submission_id INT AUTO_INCREMENT PRIMARY KEY,
    assignment_id INT NOT NULL,
    student_id INT NOT NULL,
    submission_content TEXT,
    attachment_path VARCHAR(500),
    submit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    grade DECIMAL(5,2),
    feedback TEXT,
    is_graded BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (assignment_id) REFERENCES assignments(assignment_id),
    FOREIGN KEY (student_id) REFERENCES users(user_id),
    UNIQUE KEY unique_submission (assignment_id, student_id),
    INDEX idx_assignment_id (assignment_id),
    INDEX idx_student_id (student_id)
);

-- 学生选课表
CREATE TABLE enrollments (
    enrollment_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    enrollment_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (student_id) REFERENCES users(user_id),
    FOREIGN KEY (course_id) REFERENCES courses(course_id),
    UNIQUE KEY unique_enrollment (student_id, course_id),
    INDEX idx_student_id (student_id),
    INDEX idx_course_id (course_id)
);

-- 学习进度表
CREATE TABLE learning_progress (
    progress_id INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    resource_id INT,
    progress_percentage DECIMAL(5,2) DEFAULT 0.00,
    last_access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(user_id),
    FOREIGN KEY (course_id) REFERENCES courses(course_id),
    FOREIGN KEY (resource_id) REFERENCES resources(resource_id),
    INDEX idx_student_course (student_id, course_id),
    INDEX idx_resource_id (resource_id)
);

-- 讨论区表
CREATE TABLE discussions (
    discussion_id INT AUTO_INCREMENT PRIMARY KEY,
    course_id INT NOT NULL,
    user_id INT NOT NULL,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (course_id) REFERENCES courses(course_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_course_id (course_id),
    INDEX idx_user_id (user_id)
);

-- 讨论回复表
CREATE TABLE discussion_replies (
    reply_id INT AUTO_INCREMENT PRIMARY KEY,
    discussion_id INT NOT NULL,
    user_id INT NOT NULL,
    content TEXT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (discussion_id) REFERENCES discussions(discussion_id),
    FOREIGN KEY (user_id) REFERENCES users(user_id),
    INDEX idx_discussion_id (discussion_id),
    INDEX idx_user_id (user_id)
);

-- 系统公告表
CREATE TABLE announcements (
    announcement_id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    admin_id INT NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    FOREIGN KEY (admin_id) REFERENCES users(user_id),
    INDEX idx_admin_id (admin_id)
);

-- 插入初始数据

-- 插入管理员用户
INSERT INTO users (username, password, real_name, email, user_type) VALUES
('admin', 'admin123', '系统管理员', '<EMAIL>', 'admin');

-- 插入测试教师用户
INSERT INTO users (username, password, real_name, email, user_type) VALUES
('teacher', 'teacher123', '张老师', '<EMAIL>', 'teacher'),
('teacher2', 'teacher123', '李老师', '<EMAIL>', 'teacher');

-- 插入测试学生用户
INSERT INTO users (username, password, real_name, email, user_type) VALUES
('student', 'student123', '王同学', '<EMAIL>', 'student'),
('student2', 'student123', '刘同学', '<EMAIL>', 'student'),
('student3', 'student123', '陈同学', '<EMAIL>', 'student');

-- 插入示例课程
INSERT INTO courses (course_name, course_description, teacher_id) VALUES
('Java程序设计基础', '本课程介绍Java编程语言的基础知识，包括语法、面向对象编程、异常处理等内容。', 2),
('数据库系统原理', '学习数据库的基本概念、关系模型、SQL语言、数据库设计等核心内容。', 3),
('Web前端开发', '掌握HTML、CSS、JavaScript等前端技术，学习响应式设计和现代前端框架。', 2);

-- 插入示例作业
INSERT INTO assignments (title, description, course_id, teacher_id, due_date) VALUES
('Java基础练习', '完成Java基础语法练习题，包括变量、循环、条件语句等。', 1, 2, DATE_ADD(NOW(), INTERVAL 7 DAY)),
('数据库设计作业', '设计一个简单的图书管理系统数据库，包含ER图和表结构。', 2, 3, DATE_ADD(NOW(), INTERVAL 10 DAY)),
('网页制作练习', '使用HTML和CSS制作一个个人简历网页。', 3, 2, DATE_ADD(NOW(), INTERVAL 5 DAY));

-- 插入学生选课记录
INSERT INTO enrollments (student_id, course_id) VALUES
(4, 1), (4, 3),  -- 王同学选择Java和Web前端
(5, 1), (5, 2),  -- 刘同学选择Java和数据库
(6, 2), (6, 3);  -- 陈同学选择数据库和Web前端

-- 插入系统公告
INSERT INTO announcements (title, content, admin_id) VALUES
('欢迎使用E-Learning平台', '欢迎大家使用我们的在线学习平台！在这里您可以学习各种课程，完成作业，与老师和同学互动交流。', 1),
('平台使用指南', '请仔细阅读平台使用指南，了解各项功能的使用方法。如有问题请联系管理员。', 1);

-- 创建视图：课程统计
CREATE VIEW course_statistics AS
SELECT 
    c.course_id,
    c.course_name,
    u.real_name as teacher_name,
    COUNT(DISTINCT e.student_id) as student_count,
    COUNT(DISTINCT a.assignment_id) as assignment_count,
    COUNT(DISTINCT r.resource_id) as resource_count
FROM courses c
LEFT JOIN users u ON c.teacher_id = u.user_id
LEFT JOIN enrollments e ON c.course_id = e.course_id AND e.is_active = TRUE
LEFT JOIN assignments a ON c.course_id = a.course_id AND a.is_active = TRUE
LEFT JOIN resources r ON c.course_id = r.course_id AND r.is_active = TRUE
WHERE c.is_active = TRUE
GROUP BY c.course_id, c.course_name, u.real_name;

-- 创建视图：用户统计
CREATE VIEW user_statistics AS
SELECT 
    user_type,
    COUNT(*) as user_count
FROM users 
WHERE is_active = TRUE 
GROUP BY user_type;

COMMIT;
