<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="java.util.List" %>
<%@ page import="com.elearning.entity.Course" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fn" uri="http://java.sun.com/jsp/jstl/functions" %>
<%!
    // HTML转义函数
    public static String escapeHtml(String input) {
        if (input == null) return "";
        return input.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&#x27;");
    }

    // JavaScript转义函数
    public static String escapeJs(String input) {
        if (input == null) return "";
        return input.replace("\\", "\\\\")
                   .replace("'", "\\'")
                   .replace("\"", "\\\"")
                   .replace("\n", "\\n")
                   .replace("\r", "\\r")
                   .replace("\t", "\\t");
    }
%>
<%
    // 检查用户是否已登录且为教师
    if (session.getAttribute("user") == null || !"teacher".equals(session.getAttribute("userType"))) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }

    // 生成CSRF令牌
    String csrfToken = (String) session.getAttribute("csrfToken");
    if (csrfToken == null) {
        csrfToken = java.util.UUID.randomUUID().toString();
        session.setAttribute("csrfToken", csrfToken);
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>课程管理 - E-Learning平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .header {
            background: #27ae60;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            font-size: 24px;
        }
        .nav-links {
            display: flex;
            gap: 15px;
        }
        .nav-links a {
            color: white;
            text-decoration: none;
            padding: 8px 15px;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: rgba(255,255,255,0.2);
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .page-header {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .page-header h2 {
            color: #2c3e50;
        }
        .btn {
            background: #27ae60;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #229954;
        }
        .btn.primary {
            background: #3498db;
        }
        .btn.primary:hover {
            background: #2980b9;
        }
        .btn.danger {
            background: #e74c3c;
        }
        .btn.danger:hover {
            background: #c0392b;
        }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .alert.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .form-container {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .courses-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        .course-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s;
        }
        .course-card:hover {
            transform: translateY(-5px);
        }
        .course-header {
            background: #27ae60;
            color: white;
            padding: 20px;
        }
        .course-header h3 {
            margin-bottom: 10px;
        }
        .course-header .course-id {
            font-size: 12px;
            opacity: 0.8;
        }
        .course-body {
            padding: 20px;
        }
        .course-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 15px;
        }
        .course-meta {
            font-size: 12px;
            color: #999;
            margin-bottom: 15px;
        }
        .course-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
            text-decoration: none;
            border-radius: 3px;
            border: none;
            cursor: pointer;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 20px;
            border-radius: 10px;
            width: 80%;
            max-width: 500px;
        }
        .close {
            color: #aaa;
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
        }
        .close:hover {
            color: black;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>课程管理</h1>
        <div class="nav-links">
            <a href="<%= request.getContextPath() %>/teacher/dashboard.jsp">返回控制台</a>
            <a href="<%= request.getContextPath() %>/logout">退出登录</a>
        </div>
    </div>

    <div class="container">
        <div class="page-header">
            <h2>我的课程</h2>
            <button class="btn" onclick="showAddForm()">创建新课程</button>
        </div>

        <% if (request.getAttribute("success") != null) { %>
            <div class="alert success">
                <%= request.getAttribute("success") %>
            </div>
        <% } %>

        <% if (request.getAttribute("error") != null) { %>
            <div class="alert error">
                <%= request.getAttribute("error") %>
            </div>
        <% } %>

        <!-- 添加课程表单 -->
        <div class="form-container" id="addCourseForm" style="display: none;">
            <h3>创建新课程</h3>
            <form action="<%= request.getContextPath() %>/teacher/courseManagement" method="post">
                <input type="hidden" name="action" value="add">
                <input type="hidden" name="csrfToken" value="<%= escapeHtml(csrfToken) %>">
                <div class="form-group">
                    <label for="courseName">课程名称:</label>
                    <input type="text" id="courseName" name="courseName" required maxlength="200">
                </div>
                <div class="form-group">
                    <label for="courseDescription">课程描述:</label>
                    <textarea id="courseDescription" name="courseDescription" placeholder="请输入课程的详细描述..." maxlength="1000"></textarea>
                </div>
                <div style="margin-top: 20px;">
                    <button type="submit" class="btn">创建课程</button>
                    <button type="button" class="btn primary" onclick="hideAddForm()">取消</button>
                </div>
            </form>
        </div>

        <!-- 课程列表 -->
        <div class="courses-grid">
            <%
                List<Course> courses = (List<Course>) request.getAttribute("courses");
                if (courses != null && !courses.isEmpty()) {
                    for (Course course : courses) {
            %>
            <div class="course-card">
                <div class="course-header">
                    <h3><%= course.getCourseName() %></h3>
                    <div class="course-id">课程ID: <%= course.getCourseId() %></div>
                </div>
                <div class="course-body">
                    <div class="course-description">
                        <%= course.getCourseDescription() != null ? course.getCourseDescription() : "暂无课程描述" %>
                    </div>
                    <div class="course-meta">
                        创建时间: <%= course.getCreateTime() %>
                    </div>
                    <div class="course-actions">
                        <button class="btn-small primary edit-course-btn"
                                data-course-id="<%= course.getCourseId() %>"
                                data-course-name="<%= escapeHtml(course.getCourseName()) %>"
                                data-course-description="<%= escapeHtml(course.getCourseDescription() != null ? course.getCourseDescription() : "") %>">编辑</button>
                        <a href="#" class="btn-small" style="background: #f39c12;">管理资源</a>
                        <a href="#" class="btn-small" style="background: #9b59b6;">查看学生</a>
                        <a href="<%= request.getContextPath() %>/teacher/courseManagement?action=delete&courseId=<%= course.getCourseId() %>"
                           class="btn-small danger delete-course-btn"
                           data-course-name="<%= escapeHtml(course.getCourseName()) %>">删除</a>
                    </div>
                </div>
            </div>
            <%
                    }
                } else {
            %>
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px; background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                <h3 style="color: #666; margin-bottom: 15px;">暂无课程</h3>
                <p style="color: #999;">您还没有创建任何课程，点击上方按钮创建您的第一个课程吧！</p>
            </div>
            <% } %>
        </div>
    </div>

    <!-- 编辑课程模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeEditModal()">&times;</span>
            <h3>编辑课程</h3>
            <form action="<%= request.getContextPath() %>/teacher/courseManagement" method="post">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="csrfToken" value="<%= escapeHtml(csrfToken) %>">
                <input type="hidden" id="editCourseId" name="courseId">
                <div class="form-group">
                    <label for="editCourseName">课程名称:</label>
                    <input type="text" id="editCourseName" name="courseName" required maxlength="200">
                </div>
                <div class="form-group">
                    <label for="editCourseDescription">课程描述:</label>
                    <textarea id="editCourseDescription" name="courseDescription" maxlength="1000"></textarea>
                </div>
                <div style="margin-top: 20px;">
                    <button type="submit" class="btn">保存修改</button>
                    <button type="button" class="btn primary" onclick="closeEditModal()">取消</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 显示添加表单
        function showAddForm() {
            document.getElementById('addCourseForm').style.display = 'block';
        }

        // 隐藏添加表单
        function hideAddForm() {
            document.getElementById('addCourseForm').style.display = 'none';
        }

        // 关闭编辑模态框
        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
        }

        // 页面加载完成后绑定事件
        document.addEventListener('DOMContentLoaded', function() {
            // 编辑按钮事件委托
            document.addEventListener('click', function(e) {
                if (e.target.classList.contains('edit-course-btn')) {
                    e.preventDefault();
                    var courseId = e.target.getAttribute('data-course-id');
                    var courseName = e.target.getAttribute('data-course-name');
                    var courseDescription = e.target.getAttribute('data-course-description');

                    document.getElementById('editCourseId').value = courseId;
                    document.getElementById('editCourseName').value = courseName;
                    document.getElementById('editCourseDescription').value = courseDescription || '';
                    document.getElementById('editModal').style.display = 'block';
                }

                // 删除按钮确认
                if (e.target.classList.contains('delete-course-btn')) {
                    var courseName = e.target.getAttribute('data-course-name');
                    if (!confirm('确定要删除课程《' + courseName + '》吗？')) {
                        e.preventDefault();
                        return false;
                    }
                }
            });

            // 点击模态框外部关闭
            window.onclick = function(event) {
                var modal = document.getElementById('editModal');
                if (event.target == modal) {
                    modal.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
