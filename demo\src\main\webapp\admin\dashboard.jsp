<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ page import="javax.servlet.http.HttpSession" %>
<%
    // 检查用户是否已登录且为管理员
    if (session.getAttribute("user") == null || !"admin".equals(session.getAttribute("userType"))) {
        response.sendRedirect(request.getContextPath() + "/login.jsp");
        return;
    }
%>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>管理员控制台 - E-Learning平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        .header {
            background: #2c3e50;
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .header h1 {
            font-size: 24px;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .logout-btn {
            background: #e74c3c;
            color: white;
            padding: 8px 15px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .logout-btn:hover {
            background: #c0392b;
        }
        .container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 0 20px;
        }
        .dashboard-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .card {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        .card:hover {
            transform: translateY(-5px);
        }
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 20px;
        }
        .card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .card-btn {
            display: inline-block;
            background: #3498db;
            color: white;
            padding: 12px 25px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .card-btn:hover {
            background: #2980b9;
        }
        .card-btn.danger {
            background: #e74c3c;
        }
        .card-btn.danger:hover {
            background: #c0392b;
        }
        .card-btn.success {
            background: #27ae60;
        }
        .card-btn.success:hover {
            background: #229954;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: #3498db;
            margin-bottom: 10px;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        .welcome {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .welcome h2 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        .welcome p {
            color: #666;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>E-Learning 管理员控制台</h1>
        <div class="user-info">
            <span>欢迎，<%= session.getAttribute("realName") %></span>
            <a href="<%= request.getContextPath() %>/logout" class="logout-btn">退出登录</a>
        </div>
    </div>

    <div class="container">
        <div class="welcome">
            <h2>欢迎来到管理员控制台</h2>
            <p>您可以在这里管理系统用户、查看系统统计信息、进行系统设置等操作。请选择您需要的功能模块。</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">总用户数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">总课程数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">活跃教师</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">0</div>
                <div class="stat-label">在线学生</div>
            </div>
        </div>

        <div class="dashboard-grid">
            <div class="card">
                <h3>用户管理</h3>
                <p>添加、删除、修改用户信息，管理用户权限，查看用户活动状态。</p>
                <a href="<%= request.getContextPath() %>/admin/userManagement" class="card-btn">进入用户管理</a>
            </div>

            <div class="card">
                <h3>课程管理</h3>
                <p>查看所有课程信息，管理课程状态，监控课程质量和学习效果。</p>
                <a href="<%= request.getContextPath() %>/admin/courseManagement" class="card-btn success">课程管理</a>
            </div>

            <div class="card">
                <h3>系统设置</h3>
                <p>配置系统参数，管理系统公告，设置平台基本信息和规则。</p>
                <a href="<%= request.getContextPath() %>/admin/systemSettings" class="card-btn">系统设置</a>
            </div>

            <div class="card">
                <h3>数据统计</h3>
                <p>查看平台使用统计，分析用户行为数据，生成各类报表。</p>
                <a href="<%= request.getContextPath() %>/admin/statistics" class="card-btn success">数据统计</a>
            </div>

            <div class="card">
                <h3>系统日志</h3>
                <p>查看系统运行日志，监控系统状态，排查系统问题。</p>
                <a href="<%= request.getContextPath() %>/admin/systemLogs" class="card-btn">系统日志</a>
            </div>

            <div class="card">
                <h3>数据备份</h3>
                <p>备份系统数据，恢复历史数据，确保数据安全性。</p>
                <a href="<%= request.getContextPath() %>/admin/dataBackup" class="card-btn danger">数据备份</a>
            </div>
        </div>
    </div>
</body>
</html>
