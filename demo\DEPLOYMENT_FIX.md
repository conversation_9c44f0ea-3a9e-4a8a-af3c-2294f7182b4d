# 404错误修复指南

## 问题分析
您遇到的404错误 `/demo/src/main/webapp/login` 表明项目部署配置有问题。

## 解决方案

### 1. 检查项目部署方式

#### 方法A: 使用WAR文件部署（推荐）
```bash
# 1. 构建项目
cd demo
mvn clean package

# 2. 将生成的WAR文件复制到Tomcat
cp target/demo.war $TOMCAT_HOME/webapps/

# 3. 重启Tomcat
# Windows: 
# $TOMCAT_HOME/bin/shutdown.bat
# $TOMCAT_HOME/bin/startup.bat

# 4. 访问应用
# http://localhost:8080/demo/login.jsp
```

#### 方法B: 直接部署到webapps目录
```bash
# 1. 复制整个项目到Tomcat
cp -r demo/src/main/webapp $TOMCAT_HOME/webapps/demo

# 2. 复制编译后的类文件
cp -r demo/target/classes/* $TOMCAT_HOME/webapps/demo/WEB-INF/classes/

# 3. 重启Tomcat
```

### 2. 验证部署

访问测试页面确认部署成功：
- http://localhost:8080/demo/test-deployment.jsp

### 3. 正确的访问URL

部署成功后，正确的访问URL应该是：
- 登录页面: http://localhost:8080/demo/login.jsp
- 登录处理: http://localhost:8080/demo/login
- 首页: http://localhost:8080/demo/index.jsp

### 4. 常见问题排查

1. **确认Tomcat端口**: 默认8080，如果修改过请使用正确端口
2. **确认项目名称**: WAR文件名决定了context path
3. **检查Tomcat日志**: 查看启动日志是否有错误
4. **清理缓存**: 删除Tomcat的work目录重新部署

### 5. IDE部署（如果使用Eclipse/IntelliJ）

确保IDE中的部署配置正确：
- Context path应该是 `/demo`
- 部署路径应该指向正确的webapp目录

## 修复内容

我已经修复了以下问题：
1. 注释掉了web.xml中冲突的安全约束配置
2. 创建了测试页面用于验证部署
3. 确保所有URL路径使用正确的context path

## 测试步骤

1. 重新部署项目
2. 访问 http://localhost:8080/demo/test-deployment.jsp
3. 点击测试链接验证各个功能
4. 使用测试账号登录：
   - 管理员: admin/admin123
   - 教师: teacher/teacher123
   - 学生: student/student123
