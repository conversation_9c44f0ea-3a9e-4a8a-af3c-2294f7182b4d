# E-Learning 在线学习平台

## 项目简介

这是一个基于JSP、Java、MySQL技术栈开发的在线学习平台，支持管理员、教师、学生三种用户角色，提供完整的课程管理、作业管理、用户管理等功能。

## 技术栈

- **前端**: JSP, HTML5, CSS3, JavaScript
- **后端**: Java, Servlet, JDBC
- **数据库**: MySQL 8.0
- **服务器**: Apache Tomcat 9.0+
- **构建工具**: Maven 3.6+

## 功能特性

### 管理员功能
- 用户管理（添加、删除、修改用户）
- 系统管理和配置
- 数据统计和报表
- 系统日志查看

### 教师功能
- 课程管理（创建、编辑、删除课程）
- 作业管理（布置、批改作业）
- 学生管理（查看学生进度）
- 资源管理（上传课程资料）

### 学生功能
- 课程学习（浏览课程、观看视频）
- 作业提交（完成并提交作业）
- 学习进度查看
- 互动交流（讨论区、私信）

## 环境要求

- JDK 1.8+
- MySQL 8.0+
- Apache Tomcat 9.0+
- Maven 3.6+

## 安装部署

### 1. 数据库配置

1. 创建MySQL数据库：
```sql
CREATE DATABASE elearning CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行数据库初始化脚本：
```bash
mysql -u root -p elearning < database/elearning.sql
```

3. 修改数据库连接配置（如需要）：
编辑 `src/main/java/com/elearning/util/DatabaseConnection.java` 文件中的数据库连接参数：
```java
private static final String URL = "********************************************************************************************";
private static final String USERNAME = "root";
private static final String PASSWORD = "123456";
```

### 2. 项目构建

1. 进入项目目录：
```bash
cd demo
```

2. 使用Maven构建项目：
```bash
mvn clean compile
mvn package
```

### 3. 部署到Tomcat

1. 将生成的 `target/demo.war` 文件复制到Tomcat的webapps目录
2. 启动Tomcat服务器
3. 访问 `http://localhost:8080/demo`

## 默认账号

系统预置了以下测试账号：

| 用户类型 | 用户名 | 密码 | 说明 |
|---------|--------|------|------|
| 管理员 | admin | admin123 | 系统管理员账号 |
| 教师 | teacher | teacher123 | 测试教师账号 |
| 学生 | student | student123 | 测试学生账号 |

## 项目结构

```
demo/
├── src/main/
│   ├── java/com/elearning/
│   │   ├── dao/           # 数据访问层
│   │   ├── entity/        # 实体类
│   │   ├── servlet/       # 控制器层
│   │   └── util/          # 工具类
│   └── webapp/
│       ├── admin/         # 管理员页面
│       ├── teacher/       # 教师页面
│       ├── student/       # 学生页面
│       ├── WEB-INF/       # 配置文件
│       ├── index.jsp      # 首页
│       └── login.jsp      # 登录页面
├── database/
│   └── elearning.sql     # 数据库初始化脚本
├── pom.xml               # Maven配置文件
└── README.md             # 项目说明文档
```

## 主要功能页面

### 公共页面
- `/` - 平台首页
- `/login.jsp` - 用户登录
- `/logout` - 退出登录

### 管理员页面
- `/admin/dashboard.jsp` - 管理员控制台
- `/admin/userManagement` - 用户管理

### 教师页面
- `/teacher/dashboard.jsp` - 教师控制台
- `/teacher/courseManagement` - 课程管理

### 学生页面
- `/student/dashboard.jsp` - 学生控制台

## 开发说明

### 数据库设计

主要数据表：
- `users` - 用户表
- `courses` - 课程表
- `assignments` - 作业表
- `submissions` - 作业提交表
- `enrollments` - 选课表
- `resources` - 资源表
- `discussions` - 讨论表

### 扩展开发

1. 添加新功能页面时，请遵循现有的目录结构
2. 新增Servlet时，使用`@WebServlet`注解进行URL映射
3. 数据库操作请使用DAO模式
4. 页面开发请保持统一的样式风格

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库连接参数是否正确
- 检查MySQL用户权限

### 2. 页面显示乱码
- 确保所有文件都使用UTF-8编码
- 检查web.xml中的字符编码过滤器配置

### 3. 登录后跳转失败
- 检查session配置
- 确认用户权限验证逻辑

## 联系方式

如有问题或建议，请联系开发团队。

## 许可证

本项目仅供学习和研究使用。
